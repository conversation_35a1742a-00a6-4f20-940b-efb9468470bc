**Product Requirements Document (PRD) - ruh.ai Developer Platform**
-------------------------------------------------------------------

**ruh.ai Developer PlatformProduct Requirements Document (PRD)**

**1\. Introduction**

*   **1.1 Purpose:**This document outlines the product requirements for the **ruh.ai Developer Platform**. This platform aims to provide a comprehensive, intuitive, and powerful environment for developers and administrators to build, manage, monitor, and scale AI-driven applications and services, leveraging ruh.ai's core technologies. It will serve as the central hub for interacting with AI agents, workflows, and Model Context Protocol (MCP) compliant tools. A key principle is that assets (agents, workflows, mcps) built or acquired within this platform can be seamlessly utilised within the main ruh app and the Ruh. AI Workflow Builder.
    
*   **1.2 Goals:**
    
    *   **Empower Developers:** Enable developers to easily create, configure, and deploy sophisticated AI agents and automated workflows. through various means, including building from scratch, importing, or leveraging a marketplace.
        
    *   **Simplify Integration:** Provide seamless ways to integrate custom data sources and tools using the open Model Context Protocol (MCP).
        
    *   **Enhance Visibility & Control:** Offer robust analytics, logging, and management tools including powerful search and filtering capabilities across agents, workflows, and MCPs, for monitoring performance, usage, and costs.
        
    *   **Foster a Community & Ecosystem:** Facilitate discovery, customisation and sharing of agents, workflows, and MCP Tools through a marketplace.
        
    *   **Drive Adoption:** Make ruh.ai's advanced AI capabilities accessible and easy to use, thereby increasing platform adoption and user engagement.
        
    *   **Ensure Security & Scalability:** Provide a secure and reliable platform that can scale with user needs. including secure management of credentials required for platform components.
        
    *   **Cross-Platform Utility**: Ensure that agents and workflows created or configured in the Developer Platform are directly usable in the main Ruh app and the ruh.ai Workflow Builder, fostering a cohesive ecosystem.
        

*   **1.3 Target Audience & User Personas:**
    
    *   **Persona 1: Alex - The AI Application Developer**
        
        *   _Role:_ Software Engineer.
            
        *   _Needs:_ Quickly prototype and build AI-powered features. Easily integrate diverse AI models, custom tools, and data sources. Easily acquire, customise, or build agents and workflows. Monitor application performance and troubleshoot issues. Securely manage API access for their applications.
            
        *   _Pain Points:_ Complexity in wiring up different AI services, lack of standardised ways to connect tools, difficulty in monitoring AI component behaviour.
            
    
    *   **Persona 2: Sarah - The Operations Administrator**
        
        *   _Role:_ System Administrator, DevOps Engineer, Platform Manager.
            
        *   _Needs:_ Monitor overall platform usage, credit consumption, and system health. Manage user access (future) and ensure security compliance. Understand resource utilisation patterns. Easily locate specific agents, workflows, or MCPs for oversight. 
            
        *   _Pain Points:_ Lack of centralised visibility into AI resource usage, difficulty in tracking costs associated with different AI services. Inefficient search for specific deployed assets.
            
    *   **Persona 3: Ben - The Solutions Architect / Consultant**
        
        *   _Role:_ Designs and proposes AI solutions for clients or internal teams.
            
        *   _Needs:_ Explore available AI capabilities (agents, workflows, tools). Easily demonstrate proof-of-concepts by leveraging and customising marketplace assets. Understand how different components can be combined to solve business problems.
            
        *   _Pain Points:_ Difficulty in quickly assembling and showcasing AI solutions without extensive custom coding.
            

*   **1.4 Success Metrics:**
    
    *   **Activation & Engagement:**
        
        *   Number of agents created/configured per user (distinguishing between built, marketplace, A2A).
            
        *   Number of workflows created/utilised per user (distinguishing between built and marketplace).
            
        *   Number of custom MCP Tools registered.
            
    *   **Adoption & Usage:**
        
        *   Number of API calls made through the platform.
            
        *   Growth in marketplace item (agents, workflows, MCPs) usage.
            
        *   Number of applications created.
            
        *   Usage of search and filter functionalities.
            
    *   **Satisfaction & Retention:**
        
        *   User satisfaction scores (e.g., CSAT, NPS).
            
        *   User retention rate.
            
        *   Feature adoption rates.
            
    *   **Platform Health:**
        
        *   System uptime and reliability.
            
        *   Average API response times.
            

**2\. Product Overview**

*   **2.1 Vision:**To be the leading developer-first platform for building, deploying, and managing next-generation AI applications, by providing an intuitive, flexible, and powerful suite of tools that abstract complexity and accelerate innovation. and ensure seamless integration of developed assets across the broader ruh.ai ecosystem (main ruh app, Workflow Builder).
    

*   **2.2 Key Features (High-Level):**
    
    1.  **Centralised Dashboard:** Real-time analytics and insights into platform usage and AI component performance.
        
    2.  **AI Agent Creation & Management:** 
        
        *   Create and build new AI agents with various models, tools, and knowledge bases.
            
        *   Acquire and customise agents from a Marketplace.
            
        *   Import external agents using the A2A (Agent-to-Agent) protocol.
            
        *   Powerful search and filtering capabilities.
            
    3.  **Workflow Orchestration:**
        
        *   Create and build new workflows using the integrated ruh.ai Workflow Builder, including testing capabilities.
            
        *   Acquire and customise workflows from a Marketplace.
            
        *   Powerful search and filtering capabilities.
            
    4.  **Model Context Protocol (MCP) Integration:** A standardised way to connect AI models to any data source or tool, making them discoverable and usable.
        
    5.  **Marketplace:** A central place to discover, share, and use pre-built agents, workflows, and MCP Tools.
        
    6.  **Credential Management**: Securely store and manage user credentials (e.g., API keys for third-party services) needed for agents, workflows, and MCP tools, especially those utilised from the marketplace.
        
    7.  **Application & API Key Management:** Securely manage access to ruh.ai services for various applications.
        
    8.  **Event-Driven Notifications:** Webhooks for real-time updates on important platform events.
        
    9.  **Comprehensive Logging:** Detailed activity logs for auditing, debugging, and monitoring.
        
    10.  **Cross-Platform Utility**: Agents and workflows are designed for utilisation within the main Ruh app and the ruh.ai Workflow Builder.
        

*   **2.3 Strategic Fit:**The ruh.ai Developer Platform is critical to democratising access to ruh.ai's core AI technologies. It directly supports the company's strategy of fostering a vibrant ecosystem around its AI capabilities, driving innovation, and capturing a significant share of the growing AI application development market. The ability for developers to build assets that are immediately usable in other ruh.ai products, like the main ruh app, significantly enhances this strategic value.
    

**3\. User Stories / Use Cases**

_(This section would be extensive. Here are a few examples for key modules):_

*   **Agent Management:**
    
    *   **US-AG-001:** As Alex (Developer), I want to create a new AI agent by specifying its name, purpose, and visual avatar, so I can easily identify and organise my agents.
        
    *   **US-AG-002:** As Alex, I want to define the core behaviour of my agent by writing a system prompt and selecting a specific AI model (e.g., GPT-4, Claude 3), so I can tailor its responses and capabilities.
        
    *   **US-AG-003:** As Alex, I want to enhance my agent's abilities by easily adding pre-built tools (like a calculator or web search) or custom MCP Tools, so it can perform specific actions or access relevant data.
        
    *   **US-AG-004:** As Alex, I want to provide my agent with specific knowledge by uploading documents or linking websites, so it can answer questions based on that information.
        
    *   **US-AG-005:** As Alex, I want to make my agent available for others to use via a public marketplace, or keep it private for my own applications.
        
    *   **US-AG-006:** As Ben (Solutions Architect), I want to quickly find and add an existing agent from the marketplace to demonstrate its capabilities to a client.
        
    *   **NEW US-AG-007:** As Alex, I want to import an existing A2A-compatible agent by providing its URL, so I can integrate it into my ruh.ai environment.
        
    *   **NEW US-AG-008: As Alex,** I want to search my list of agents by name or tag (e.g., "customer-service") and filter by status (e.g., "published") to quickly find the agent I need.
        
    *   **NEW US-AG-009:** As Alex, after adding an agent from the marketplace, I want to customise its system prompt and available tools, so it better fits my specific use case.
        
    *   **NEW US-AG-010:** As Alex, I want to use an agent I built in the Developer Platform directly within the main ruh app.
        
*   **Workflow Management:**
    
    *   **NEW US-WF-001:** As Alex, I want to create a new workflow from scratch using the ruh.ai Workflow Builder, connecting different agents and tools, so I can automate a complex business process.
        
    *   **NEW US-WF-002:** As Alex, I want to test my workflow within the builder interface and also be able to open it in a separate, dedicated ruh.ai Workflow Builder instance for more focused debugging.
        
    *   **NEW US-WF-003:** As Ben, I want to browse the marketplace for pre-built workflows (e.g., "customer onboarding") and add one to my account to accelerate my solution design.
        
    *   **NEW US-WF-004:** As Alex, after adding a workflow from the marketplace, I want to modify its steps or change the agents it uses, so I can adapt it to my organization's specific needs.
        
    *   **NEW US-WF-005:** As Alex, I want to search my list of workflows by name or the agents they include and filter by last modified date to efficiently manage my automations.
        
    *   **NEW US-WF-006:** As Alex, I want to orchestrate agents I built in the Developer Platform using the ruh.ai Workflow Builder.
        
*   **MCP Tool Management**
    
    *   **US-MCP-001:** As Alex, I want to register my company's internal customer database as an MCP Tool by providing its API endpoint and how it conforms to the MCP protocol, so my AI agents can securely query customer information.
        
    *   **US-MCP-002:** As Alex, I want to browse a marketplace of third-party MCP Tools (e.g., a weather service, a stock market data provider) and add them to my available resources.
        
    *   NEW US-MCP-003: As Sarah, I want to search for a specific MCP Tool by name or type within our registered tools to check its configuration.
        
*   **Credential Management:**
    
    *   **NEW US-CM-001:** As Alex, I want to securely store my API key for a third-party service (e.g., OpenAI) in the platform, so that agents or workflows I use from the marketplace can utilize this service without me re-entering the key.
        
    *   **NEW US-CM-002:** As Alex, when configuring a marketplace agent that requires an external API key, I want to be able to select from my securely stored credentials.
        
*   **Overview Dashboard:**
    
    *   **US-OV-001:** As Sarah (Administrator), I want to see a dashboard with key metrics like total API calls, active agents, and credit usage over the last 30 days, so I can monitor platform health and costs.
        
    *   **US-OV-002:** As Alex, I want to quickly see my most active agents and recent API request logs on my overview page, so I can get a snapshot of my current activity.
        
*   **API Keys & Apps:**
    
    *   **US-AK-001:** As Alex, I want to create separate API keys for my "Staging" and "Production" applications, so I can manage access and track usage independently.
        

**4\. Detailed Feature Requirements**

_(This section would map closely to the Functional Requirements in the TRD but phrased from a user/product perspective. Example for one module):_

*   **4.1 Module: Overview Dashboard**
    
    *   **Purpose:** Provide users with an at-a-glance summary of their platform activity, resource usage, and key performance indicators.
        
    *   **Requirements:**
        
        *   Display customizable widgets for key metrics:
            
            *   Credit Usage (with trends and forecasts if possible).
                
            *   Active Agents count.
                
            *   Total Agent Requests (with historical graph).
                
            *   Total Workflow Requests (with historical graph).
                
            *   Number of custom MCP Tools registered/active.
                
            *   Application-specific usage (if an app context is selected).
                
        *   Show a feed of the latest API requests (summary: endpoint, status, timestamp).
            
        *   Show a feed of recent platform events/notifications.
            
        *   Allow users to filter dashboard data by common time ranges (e.g., Last 24 hours, Last 7 Days, Last 30 Days, Custom Range).
            
        *   Ensure the dashboard loads quickly and presents information clearly.
            
*   **4.2 Module: Agent Management**
    
    *   **Purpose:** Allow users to create, configure, test, deploy, and manage the lifecycle of AI agents. Agents created or configured here are usable in the main Ruh app and ruh.ai Workflow Builder.
        
    *   **Requirements:**
        
        *   **Intuitive Agent Creation Wizard:**
            
            *   **Agent Foundation:** Name, description, avatar (upload/AI-generate), category selection.
                
            *   **Core Logic:** System prompt input (with AI optimisation suggestions), AI model selection (from various providers like OpenAI, Anthropic, etc.).
                
            *   **Capabilities:** Easy addition of standard tools, MCP Tools (from marketplace or user-registered), and existing workflows.
                
            *   **Knowledge Base:** Support for document uploads, website links, and direct text input as knowledge sources.
                
            *   **Configuration Variables:** Interface to define and set key-value pairs for agent use.
                
            *   **Publishing Controls:** Options to save as a draft, publish, and set visibility (public marketplace/private).
                
        *   **Acquire from Marketplace:**
            
            *   Browse and search the ruh.ai Marketplace for pre-built agents.
                
            *   Add selected agents to the user's workspace.
                
            *   Customisation: Ability to customise marketplace-acquired agents (e.g., edit prompt, change tools, update knowledge base, modify configuration variables) after adding them. This may involve "forking" or creating a user-specific version**.**
                
        *   **Import A2A (Agent-to-Agent) Agent:**
            
            *    Interface to import an external agent by providing its A2A protocol-compliant URL.
                
            *    Configuration options for the imported agent as per A2A specifications.
                
        *   **A2A (Agent-to-Agent) Protocol Support:**
            
            *   Add external agents via A2A URL.
                
            *   Option to make user-created agents A2A compatible, generating an access route.
                
        *   **Agent Utilisation:**
            
            *   In-platform testing interface (e.g., chat).
                
            *   API access details and code snippets for programmatic execution.
                
        *   **Search and Filter:**
            
            *   Robust search by agent name, description, tags.
                
            *   Filters for: agent source (built, marketplace, A2A), status (draft, published), AI model, creation date, and associated tools.
                
        *   **Management View:** List, view details (configuration, stats), edit, and delete agents.
            
*   **4.3 Module: Workflow Management**
    
    *   **Purpose:** Enable users to automate sequences of tasks, potentially involving multiple agents and tools. Workflows created or configured here are usable in the main Ruh app (if applicable) and can orchestrate agents from the developer platform.
        
    *   **Requirements:**
        
        *   Workflow Creation & Acquisition Methods:
            
            *   Create and Build a Workflow:
                
                *   Seamless redirection to/integration with the ruh.ai Workflow Builder for visual creation and editing.
                    
                *   Ability to incorporate agents (from the Agent Management module), MCP Tools, and other services.
                    
                *   Testing: In-building testing capabilities.
                    
                *   Dedicated Builder Access: Option to open the workflow in a separate, full-featured ruh.ai Workflow Builder instance for advanced editing and testing.
                    
            *   Acquire from Marketplace:
                
                *    Browse and search the ruh.ai Marketplace for pre-built workflows.
                    
                *    Add selected workflows to the user's workspace.
                    
            *    Customisation: Ability to customise marketplace-acquired workflows (e.g., modify steps, change parameters, swap agents/tools) by opening them in the ruh.ai Workflow Builder. This may involve "forking" or creating a user-specific version.
                
        *    Management View:
            
            *   List all user-created and marketplace-added workflows.
                
            *   View workflow details (description, constituent steps/agents, last run status).
                
            *   Get API execution snippets (TypeScript, Python, cURL) or other invocation methods.
                
            *   Clear indication of how to trigger/use the workflow from the main Ruh app or other services.
                
            *   Edit (via Workflow Builder), duplicate, and delete workflows.
                
        *   Search and Filter:
            
            *   Robust search by workflow name, description, tags.
                
            *   Filters for: workflow source (built, marketplace), status, creation date, constituent agents/tools.
                
*   **4.4 Module: MCP Tool (Model Context Protocol) Management**
    
    *   **Purpose:** Allow users to connect their LLMS to any data source or tool in a standardised way.
        
    *   **Requirements:**
        
        *   **Register Custom MCP Tools:** Interface for users to register their own services/applications that adhere to the MCP protocol by providing:
            
            *   Name, description.
                
            *   Service endpoint URL.
                
            *   MCP interface definition/schema.
                
            *   Authentication credentials for the platform to access the tool.
                
        *   **Marketplace for MCP Tools:** Discover and add pre-built/third-party MCP Tools.
            
        *   **View & Manage:** List registered/added MCP Tools, view their details (how they implement MCP, required config), edit custom registrations, and remove/de-register.
            
*   **4.5 Module: Application & API Key Management**
    
    *   **Purpose:** Provide a secure way for users to manage access for their applications connecting to ruh.ai.
        
    *   **Requirements:**
        
        *   Create logical "Applications" as containers for API keys and associated analytics.
            
        *   Generate multiple API key pairs (public/private) per Application.
            
        *   Clearly display public keys and provide private keys securely (one-time view).
            
        *   Ability to revoke API keys.
            
        *   View API request logs and stats filtered by Application.
            
*   **4.6 Module: Webhook Management**
    
    *   **Purpose:** Allow users to receive real-time notifications for important platform events.
        
    *   **Requirements:**
        
        *   Create webhooks by specifying a target URL and subscribing to event types (e.g., workflow completion, agent response).
            
        *   Option to add a secret for payload verification.
            
        *   Manage (view, edit, enable/disable, delete) existing webhooks.
            
        *   View webhook delivery attempt logs.
            
*   **4.7 Module: Activity Logging**
    
    *   **Purpose:** Provide users with a comprehensive audit trail of actions and events within their account.
        
    *   **Requirements:**
        
        *   Log key events: API calls, agent executions, workflow runs, resource creation/modification, webhook deliveries.
            
        *   Display logs with timestamps, event types, status, and relevant details.
            
        *   Allow filtering by time, event type, status, and associated resource ID.
            
*   **4.8 Module: User Profile**
    
    *   **Purpose:** Allow users to manage their personal information and view account-related summaries.
        
    *   **Requirements:**
        
        *   View and edit profile details (name, organisation).
            
        *   Link to billing information/portal.
            
        *   Summary of active applications and API keys.
            
*   **4.9 Credential Management (Secure Vault)**
    
    *   **Purpose:** To provide a secure and centralized way for developers to store and manage credentials (e.g., API keys, OAuth tokens, usernames/passwords) required by agents, workflows, or MCP Tools, particularly those utilized from the marketplace or custom MCPs that access third-party services.
        
    *    **Requirements:**
        
        *    **Secure Credential Storage: (Interface to add, credential types, encryption, masking)**
            
        *    **Credential Management: (List, update, delete)**
            
        *    **Credential Utilization: (Selection during configuration, secure passing, audit logs)**
            
*   **4.10 Module: Platform Settings**
    
    *   **Purpose:** Provide users with options to customise their platform experience.
        
    *   **Requirements:**
        
        *   Manage notification preferences.
            
        *   Theme selection (Light/Dark mode).
            
        *   Data export options (e.g., activity logs).
            

**5\. Design & UX Considerations**

*   **Intuitive Navigation:** Clear, consistent, and easy-to-understand navigation throughout the platform.
    
*   **User-Friendly Interfaces:** Clean, modern design with minimal clutter. Forms and wizards should be straightforward and guide the user.
    
*   **Efficient Search & Filtering**: Search bars should be prominent on relevant list views (Agents, Workflows, MCPs). Filters should be easily accessible and apply quickly.
    
*   **Clear Indication of Customisation**: When a user adds an item from the marketplace, it should be clear how they can customise it, and whether their customisations create a new private version.
    
*   **Seamless Credential Selection**: Integrating credentials from the Secure Vault into agent/workflow/MCP configuration should be intuitive.
    
*   **Performance:** The platform must be responsive and fast, even with large amounts of data.
    
*   **Feedback & Error Handling:** Provide clear feedback for user actions (success, failure, progress). Error messages should be informative and helpful.
    
*   **Accessibility:** Adhere to accessibility standards (e.g., WCAG) to ensure the platform is usable by people with disabilities.
    
*   **Consistency:** Maintain consistent design patterns, terminology, and interaction models across all modules.
    
*   **Onboarding:** Consider in-app tours or tooltips for new users or complex features, especially for marketplace customisation and credential management.
    

**6\. Release Criteria (High-Level - MVP)**

*   **Core Functionality:**
    
    *   User authentication and basic profile management.
        
    *   Agent Management MVP:
        
        *   Create and build basic agent (prompt, model, simple tool/knowledge).
            
        *   Add agent from marketplace (basic, no deep customization yet).
            
        *   Basic search/filter for agents.
            
    *   Workflow Management MVP:
        
        *   Add workflow from marketplace (basic, no deep customization yet).
            
        *   Redirect to builder for new workflow creation; API execution info.
            
        *   Basic search/filter for workflows.
            
    *   Application and API key generation/management.
        
    *   Basic Overview dashboard with key metrics.
        
    *   Activity logging for critical events.
        
    *   Credential Management MVP: Secure storage and selection of at least one common credential type (e.g., generic API key) for marketplace items.
        
    *   Cross-Platform Link (Informational): Documentation on how to use created agents/workflows in main ruh app / Workflow Builder, even if deep integration is post-MVP.
        
*   **Stability & Performance:** Key user flows are tested and performant. No critical or blocker bugs.
    
*   **Security:** Core security considerations (authentication, API key handling, credential storage) are implemented.
    
*   **Basic Documentation:** Essential user guides for core features are available.
    

**7\. Future Considerations / Potential Enhancements**

*   **Support for more diverse A2A protocol features.**
    
*   **AI-assisted agent/workflow creation**
    
*   **Team collaboration features (shared agents, workflows, credentials with roles/permissions).**
    
*   **More sophisticated analytics and cost management tools.**
    

**Basic designs**Developer platform: [https://ruh-ai-developer-hub.lovable.app/](https://ruh-ai-developer-hub.lovable.app/)Ruh Documentation: [https://ruh-ai-docs-design.lovable.app/](https://ruh-ai-docs-design.lovable.app/)Ruh market place: [https://agentic-nexus-flow-82.lovable.app/](https://agentic-nexus-flow-82.lovable.app/)

For Reference:agents:1. [https://app.on-demand.io/overview](https://app.on-demand.io/overview)

2\. [https://app.relevanceai.com/agents](https://app.relevanceai.com/agents)

3\. [https://cloud.dify.ai/apps](https://cloud.dify.ai/apps)

4\. [https://cloud.langfuse.com/project/clkpwwm0m000gmm094odg11gi](https://cloud.langfuse.com/project/clkpwwm0m000gmm094odg11gi)Marketplace

1.  [https://smithery.ai/](https://smithery.ai/)
    
2.  [https://marketplace.dify.ai/](https://marketplace.dify.ai/)