## Project info

## How can I edit this code?

There are several ways of editing your application.

**Use**

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

### Using Docker

This project can be easily deployed using Docker. Follow these steps to build and run the application using Docker:

#### Prerequisites

- Docker installed on your machine ([Install Docker](https://docs.docker.com/get-docker/))

#### Building and Running with Docker

```sh
# Build the Docker image
docker build -t ruh-ai-docs-design .

# Run the container
docker run -p 3000:3000 ruh-ai-docs-design
```

The application will be available at http://localhost:3000

#### Using Docker Compose

Alternatively, you can use Docker Compose for a more streamlined experience:

```sh
# Build and run using Docker Compose
docker-compose up --build

# To run in detached mode (background)
docker-compose up -d --build

# To stop the container
docker-compose down
```

#### Docker Configuration

The Docker setup includes:

- Multi-stage build for optimized image size
- Production-ready Node.js environment
- Custom server startup script for better reliability
- Proper handling of environment variables

#### Customizing Docker Deployment

You can customize the Docker deployment by setting environment variables:

```sh
# Run with custom port
docker run -p 8080:8080 -e PORT=8080 ruh-ai-docs-design

# Run with custom environment variables
docker run -p 3000:3000 -e NODE_ENV=production -e VITE_API_URL=https://api.example.com ruh-ai-docs-design
```

Or using Docker Compose by editing the `docker-compose.yml` file:

```yaml
services:
  app:
    # ...
    environment:
      - NODE_ENV=production
      - PORT=3000
      - VITE_API_URL=https://api.example.com
```

#### Troubleshooting Docker Deployment

If you encounter issues with the Docker deployment, try these solutions:

1. **Connection refused errors**:

   - Ensure the container is running: `docker ps`
   - Check container logs: `docker logs <container_id>`
   - Verify the port mapping: `docker port <container_id>`

2. **Container exits immediately**:

   - Check logs for errors: `docker logs <container_id>`
   - Try running in interactive mode: `docker run -it ruh-ai-docs-design sh`

3. **Changes not reflecting**:
   - Rebuild the image: `docker build --no-cache -t ruh-ai-docs-design .`
   - Remove old containers: `docker-compose down` before `docker-compose up --build`

## Can I connect a custom domain to my project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.
