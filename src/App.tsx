
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/components/ThemeProvider";
import Index from "./pages/Index";
import DocsLayout from "./pages/DocsLayout";
import AgentOverview from "./pages/docs/AgentOverview";
import ApiReference from "./pages/api/ApiReference";
import Community from "./pages/community/Community";
import NotFound from "./pages/NotFound";
import HelpCenter from "./pages/help/HelpCenter";
import Login from "./pages/auth/Login";
import Signup from "./pages/auth/Signup";
import WhatsNew from "./pages/docs/development/WhatsNew";
import Roadmap from "./pages/docs/development/Roadmap";
import VersionHistory from "./pages/docs/development/VersionHistory";

// Create a QueryClient instance
const queryClient = new QueryClient();

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/docs" element={<DocsLayout />}>
                <Route index element={<AgentOverview />} />
                <Route path="agents/intro" element={<AgentOverview />} />
                <Route path="development/whats-new" element={<WhatsNew />} />
                <Route path="development/roadmap" element={<Roadmap />} />
                <Route path="development/versions" element={<VersionHistory />} />
                {/* Add other doc routes as needed */}
              </Route>
              <Route path="/api" element={<DocsLayout />}>
                <Route index element={<ApiReference />} />
                <Route path="*" element={<ApiReference />} />
              </Route>
              <Route path="/community" element={<Community />} />
              <Route path="/help-center" element={<HelpCenter />} />
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
