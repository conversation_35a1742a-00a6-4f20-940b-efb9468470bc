
import { Header } from "@/components/Header";
import { DocsSidebar } from "@/components/Sidebar";
import { Footer } from "@/components/Footer";
import { TableOfContents } from "@/components/TableOfContents";
import { Outlet, useLocation } from "react-router-dom";
import { ApiSidebar } from "@/components/ApiSidebar";

export default function DocsLayout() {
  const location = useLocation();
  const isApiPage = location.pathname.startsWith("/api");
  
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <div className="container flex-1 items-start md:grid md:grid-cols-[240px_minmax(0,1fr)] lg:grid-cols-[240px_minmax(0,1fr)] xl:grid-cols-[240px_minmax(0,1fr)_200px] md:gap-6 lg:gap-10">
        {isApiPage ? <ApiSidebar /> : <DocsSidebar />}
        <main className="relative py-6 lg:gap-10 lg:py-8 w-full">
          <Outlet />
        </main>
        {!isApiPage && <TableOfContents />}
      </div>
      <Footer />
    </div>
  );
}
