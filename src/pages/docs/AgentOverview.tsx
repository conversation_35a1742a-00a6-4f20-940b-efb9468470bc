
import { CodeBlock } from "@/components/CodeBlock";

export default function AgentOverview() {
  return (
    <div className="max-w-3xl mx-auto">
      <h1 id="overview" className="h1">AI Agents</h1>
      <p className="text-lg text-muted-foreground mt-4 mb-8">
        Understand the core concepts of AI agents in the Ruh.ai platform and how to leverage them in your workflows.
      </p>

      <h2 id="what-are-agents" className="h2">What are AI Agents?</h2>
      <p className="leading-7 [&:not(:first-child)]:mt-6">
        AI Agents in Ruh.ai are autonomous entities that can perform tasks, make decisions, and interact with other systems based on their configured capabilities. Think of them as digital assistants that can be programmed to handle specific functions within your workflows.
      </p>
      
      <h3 id="agent-architecture" className="h3">Agent Architecture</h3>
      <p className="leading-7 [&:not(:first-child)]:mt-6">
        Each Ruh.ai agent consists of several key components:
      </p>
      
      <ul className="my-6 ml-6 list-disc [&>li]:mt-2">
        <li>
          <strong>Core Model:</strong> The underlying AI model that powers the agent's capabilities
        </li>
        <li>
          <strong>Memory Systems:</strong> Short and long-term memory storage for context retention
        </li>
        <li>
          <strong>Tool Interfaces:</strong> Connections to external tools and data sources
        </li>
        <li>
          <strong>Communication Channels:</strong> Methods for interacting with users and other agents
        </li>
      </ul>

      <h2 id="agent-types" className="h2">Agent Types</h2>
      <p className="leading-7 [&:not(:first-child)]:mt-6">
        Ruh.ai offers several specialized agent types to suit different use cases:
      </p>

      <div className="grid gap-6 md:grid-cols-2 my-6">
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-2">Task Agents</h3>
          <p className="text-muted-foreground">Focused on completing specific tasks efficiently without ongoing interaction.</p>
        </div>
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-2">Assistant Agents</h3>
          <p className="text-muted-foreground">Designed for interactive conversations and providing helpful information to users.</p>
        </div>
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-2">Research Agents</h3>
          <p className="text-muted-foreground">Specialized in gathering and synthesizing information from multiple sources.</p>
        </div>
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-2">Workflow Agents</h3>
          <p className="text-muted-foreground">Orchestrate complex processes involving multiple steps and decision points.</p>
        </div>
      </div>

      <h2 id="getting-started" className="h2">Getting Started with Agents</h2>
      <p className="leading-7 [&:not(:first-child)]:mt-6">
        Creating your first agent is simple. Use the agent creation API or the web interface to define your agent's capabilities:
      </p>

      <CodeBlock 
        language="javascript"
        code={`// Creating a basic agent using the Ruh.ai API
const ruhClient = new RuhAI(API_KEY);

const myAgent = await ruhClient.agents.create({
  name: "Customer Support Assistant",
  description: "Handles customer inquiries about products and services",
  model: "gpt-4",
  tools: ["knowledge-base", "ticket-system"],
  memory: {
    conversationSteps: 10,
    longTermStorage: true
  }
});

// The agent can now be accessed via its ID
console.log(\`Agent created with ID: \${myAgent.id}\`);`}
      />

      <h3 id="agent-permissions" className="h3">Agent Permissions</h3>
      <p className="leading-7 [&:not(:first-child)]:mt-6">
        Security is a critical aspect of agent configuration. Ruh.ai uses a granular permissions system to control what actions an agent can perform:
      </p>
      
      <CodeBlock 
        language="javascript"
        code={`// Setting up agent permissions
await ruhClient.agents.updatePermissions(myAgent.id, {
  allowedDomains: ["api.mycompany.com", "knowledge.mycompany.com"],
  allowExternalSearch: true,
  maxTokenConsumption: 10000,
  accessibleTools: ["internal-docs", "customer-database", "email-sender"],
  userDataAccess: "anonymized"
});`}
      />

      <h2 id="next-steps" className="h2">Next Steps</h2>
      <p className="leading-7 [&:not(:first-child)]:mt-6">
        Now that you understand the basics of AI agents in Ruh.ai, you can:
      </p>

      <ul className="my-6 ml-6 list-disc [&>li]:mt-2">
        <li>Learn how to <a href="/docs/agents/creating" className="font-medium text-primary underline underline-offset-4">create and configure agents</a> for your specific use cases</li>
        <li>Explore the <a href="/docs/agents/lifecycle" className="font-medium text-primary underline underline-offset-4">agent lifecycle management</a> best practices</li>
        <li>Understand <a href="/docs/agents/capabilities" className="font-medium text-primary underline underline-offset-4">agent capabilities and limitations</a></li>
        <li>Review <a href="/docs/agents/best-practices" className="font-medium text-primary underline underline-offset-4">best practices</a> for working with AI agents</li>
      </ul>
    </div>
  );
}
