
import React from "react";

type Version = {
  version: string;
  date: string;
  major: boolean;
  changes: {
    category: "New Features" | "Improvements" | "Bug Fixes" | "API Changes" | "Documentation";
    items: string[];
  }[];
};

const versions: Version[] = [
  {
    version: "1.5.0",
    date: "May 1, 2025",
    major: true,
    changes: [
      {
        category: "New Features",
        items: [
          "Added parallel execution capability for workflow steps",
          "Introduced agent-to-agent messaging protocol",
          "Added support for collaborative problem-solving between agents",
          "Implemented memory sharing capabilities with configurable permissions"
        ]
      },
      {
        category: "Improvements",
        items: [
          "Improved error handling and recovery options",
          "New visualization options for complex workflow paths",
          "Enhanced token usage monitoring tools"
        ]
      },
      {
        category: "API Changes",
        items: [
          "Added new endpoints for agent collaboration",
          "Extended workflow execution API with parallel processing options",
          "Added management endpoints for shared memory resources"
        ]
      }
    ]
  },
  {
    version: "1.4.2",
    date: "April 15, 2025",
    major: false,
    changes: [
      {
        category: "Bug Fixes",
        items: [
          "Fixed memory leaks in long-running workflow processes",
          "Resolved authentication token refresh issues",
          "Fixed inconsistent behavior in agent context switching"
        ]
      },
      {
        category: "Improvements",
        items: [
          "Optimized token usage calculations",
          "Improved response time for complex agent queries",
          "Reduced latency in cross-agent communications"
        ]
      }
    ]
  },
  {
    version: "1.4.0",
    date: "March 20, 2025",
    major: true,
    changes: [
      {
        category: "New Features",
        items: [
          "Introduced agent memory persistence options",
          "Added new specialized research agent template",
          "Implemented advanced RAG capabilities for all agent types"
        ]
      },
      {
        category: "Documentation",
        items: [
          "Complete API reference documentation",
          "New tutorials for common use cases",
          "Updated best practices for enterprise deployments"
        ]
      },
      {
        category: "API Changes",
        items: [
          "Added new memory management endpoints",
          "Extended agent creation API with template options",
          "Updated authentication flow to support SSO providers"
        ]
      }
    ]
  },
  {
    version: "1.3.1",
    date: "February 28, 2025",
    major: false,
    changes: [
      {
        category: "Bug Fixes",
        items: [
          "Fixed issue with workflow step timeouts",
          "Resolved race condition in parallel task execution",
          "Fixed incorrect error messages in API responses"
        ]
      },
      {
        category: "Improvements",
        items: [
          "Enhanced logging for debugging purposes",
          "Improved error reporting for workflow failures",
          "Performance optimizations for large knowledge bases"
        ]
      }
    ]
  },
  {
    version: "1.3.0",
    date: "February 5, 2025",
    major: true,
    changes: [
      {
        category: "New Features",
        items: [
          "Added support for custom tool development",
          "Introduced role-based access control",
          "Added team collaboration features"
        ]
      },
      {
        category: "Improvements",
        items: [
          "Redesigned agent configuration interface",
          "Enhanced workflow visualization",
          "Improved knowledge retrieval accuracy"
        ]
      }
    ]
  },
  {
    version: "1.2.0",
    date: "January 10, 2025",
    major: true,
    changes: [
      {
        category: "New Features",
        items: [
          "Introduced workflow templates",
          "Added support for conditional branching in workflows",
          "Enhanced agent memory with semantic search"
        ]
      },
      {
        category: "API Changes",
        items: [
          "Revised authentication endpoints",
          "Added bulk operations for agents and workflows",
          "Updated rate limiting implementation"
        ]
      }
    ]
  },
  {
    version: "1.1.0",
    date: "December 5, 2024",
    major: true,
    changes: [
      {
        category: "New Features",
        items: [
          "Added tool integration framework",
          "Introduced basic workflow capabilities",
          "Added support for persistent agent memory"
        ]
      }
    ]
  },
  {
    version: "1.0.0",
    date: "November 1, 2024",
    major: true,
    changes: [
      {
        category: "New Features",
        items: [
          "Initial public release",
          "Core agent capabilities",
          "Basic API functionality",
          "Console for agent management"
        ]
      }
    ]
  }
];

export default function VersionHistory() {
  return (
    <div className="max-w-3xl mx-auto">
      <h1 id="version-history" className="h1">Version History</h1>
      <p className="text-lg text-muted-foreground mt-4 mb-8">
        Complete history of Ruh.ai platform versions, changes, and updates.
      </p>

      <div className="space-y-12">
        {versions.map((version, idx) => (
          <section key={version.version} id={`version-${version.version}`}>
            <div className="flex items-center gap-2 mb-4">
              {idx === 0 && (
                <span className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-primary text-primary-foreground">LATEST</span>
              )}
              <h2 className="h3 mb-0">Version {version.version}</h2>
              <span className="text-muted-foreground text-sm">({version.date})</span>
            </div>

            <div className={`border-l-2 ${idx === 0 ? "border-primary/20" : "border-muted"} pl-4 space-y-6`}>
              {version.changes.map((changeGroup, groupIdx) => (
                <div key={groupIdx}>
                  <h3 className="font-semibold text-lg">{changeGroup.category}</h3>
                  <ul className="mt-2 space-y-2 list-disc pl-5">
                    {changeGroup.items.map((item, itemIdx) => (
                      <li key={itemIdx}>{item}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </section>
        ))}
      </div>
    </div>
  );
}
