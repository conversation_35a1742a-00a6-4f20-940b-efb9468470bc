
import { CodeBlock } from "@/components/CodeBlock";

export default function Roadmap() {
  return (
    <div className="max-w-3xl mx-auto">
      <h1 id="roadmap" className="h1">Roadmap</h1>
      <p className="text-lg text-muted-foreground mt-4 mb-8">
        Upcoming features and development plans for the Ruh.ai platform.
      </p>

      <div className="grid gap-12">
        <section>
          <h2 id="q3-2025" className="h2">Q3 2025 - Advanced Orchestration</h2>
          <div className="border-l-2 border-primary/20 pl-6 py-4 space-y-8">
            <div className="relative">
              <div className="absolute -left-[33px] size-4 rounded-full bg-primary"></div>
              <h3 className="font-semibold text-xl">Multi-Agent Orchestration Framework</h3>
              <div className="mt-3 space-y-3">
                <p>A comprehensive framework for managing teams of specialized agents working together on complex tasks.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Dynamic task allocation between agents</li>
                  <li>Hierarchical agent structures with supervisor and worker roles</li>
                  <li>Advanced conflict resolution strategies</li>
                </ul>
                <div className="bg-muted p-4 rounded-md mt-3">
                  <CodeBlock 
                    language="javascript"
                    code={`// Preview: Setting up an agent team
const agentTeam = await ruhClient.agentTeams.create({
  name: "Research & Analysis",
  structure: "hierarchical",
  supervisor: {
    model: "gpt-4-turbo",
    role: "coordinator"
  },
  workers: [
    {
      role: "researcher",
      model: "gpt-4",
      specialized: true,
      tools: ["web-search", "academic-database"]
    },
    {
      role: "analyst",
      model: "gpt-4",
      specialized: true,
      tools: ["data-analysis", "visualization"]
    }
  ]
});`}
                  />
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="absolute -left-[33px] size-4 rounded-full bg-primary"></div>
              <h3 className="font-semibold text-xl">Enhanced Workflow Automation</h3>
              <div className="mt-3 space-y-2">
                <p>More powerful workflow capabilities with advanced scheduling, triggers, and conditions.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Time-based and event-based workflow triggers</li>
                  <li>Conditional branching with complex criteria</li>
                  <li>Workflow templates for common business processes</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <section>
          <h2 id="q4-2025" className="h2">Q4 2025 - Enterprise Features</h2>
          <div className="border-l-2 border-muted pl-6 py-4 space-y-8">
            <div className="relative">
              <div className="absolute -left-[33px] size-4 rounded-full bg-muted-foreground"></div>
              <h3 className="font-semibold text-xl">Advanced Security Features</h3>
              <div className="mt-3 space-y-2">
                <p>Enterprise-grade security enhancements for organizations with strict compliance requirements.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Fine-grained access controls and permission management</li>
                  <li>Audit logging for agent actions and system changes</li>
                  <li>GDPR and HIPAA compliance tooling</li>
                </ul>
              </div>
            </div>

            <div className="relative">
              <div className="absolute -left-[33px] size-4 rounded-full bg-muted-foreground"></div>
              <h3 className="font-semibold text-xl">On-Premise Deployment</h3>
              <div className="mt-3 space-y-2">
                <p>Full support for deploying Ruh.ai within private infrastructure.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Kubernetes-based deployment templates</li>
                  <li>Air-gapped installation options</li>
                  <li>Integration with private LLM deployments</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <section>
          <h2 id="q1-2026" className="h2">Q1 2026 - Next-Gen Capabilities</h2>
          <div className="border-l-2 border-muted pl-6 py-4 space-y-8">
            <div className="relative">
              <div className="absolute -left-[33px] size-4 rounded-full bg-muted-foreground"></div>
              <h3 className="font-semibold text-xl">Multimodal Agent Interactions</h3>
              <div className="mt-3 space-y-2">
                <p>Support for agents that can process and generate multiple types of media.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Vision-enabled agents for image analysis and generation</li>
                  <li>Audio processing and generation capabilities</li>
                  <li>Mixed-media knowledge processing</li>
                </ul>
              </div>
            </div>

            <div className="relative">
              <div className="absolute -left-[33px] size-4 rounded-full bg-muted-foreground"></div>
              <h3 className="font-semibold text-xl">Advanced Learning Capabilities</h3>
              <div className="mt-3 space-y-2">
                <p>Enabling agents to improve over time through various learning mechanisms.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Feedback-based learning from human interactions</li>
                  <li>Knowledge distillation between agent generations</li>
                  <li>Specialized fine-tuning options</li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      </div>

      <div className="mt-12 p-4 border rounded-md bg-muted/50">
        <h3 className="font-medium text-lg">Provide Feedback</h3>
        <p className="text-muted-foreground mt-2">
          Have suggestions for our roadmap? We'd love to hear from you! Share your ideas and feedback with our team.
        </p>
        <a href="#" className="inline-block mt-3 text-primary hover:underline">Submit Roadmap Feedback</a>
      </div>
    </div>
  );
}
