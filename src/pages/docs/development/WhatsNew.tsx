
import { CodeBlock } from "@/components/CodeBlock";

export default function WhatsNew() {
  return (
    <div className="max-w-3xl mx-auto">
      <h1 id="whats-new" className="h1">What's New</h1>
      <p className="text-lg text-muted-foreground mt-4 mb-8">
        Latest updates, improvements, and new features in Ruh.ai platform.
      </p>

      <div className="space-y-12">
        {/* Latest Version Section */}
        <section>
          <div className="flex items-center gap-2 mb-4">
            <span className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-primary text-primary-foreground">LATEST</span>
            <h2 id="version-1-5-0" className="h3 mb-0">Version 1.5.0</h2>
            <span className="text-muted-foreground text-sm">(May 1, 2025)</span>
          </div>

          <div className="border-l-2 border-primary/20 pl-4 space-y-6">
            <div>
              <h3 className="font-semibold text-lg">Workflow Engine Improvements</h3>
              <ul className="mt-2 space-y-2 list-disc pl-5">
                <li>Added parallel execution capability for workflow steps</li>
                <li>Improved error handling and recovery options</li>
                <li>New visualization options for complex workflow paths</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-lg">Enhanced Agent Communication</h3>
              <ul className="mt-2 space-y-2 list-disc pl-5">
                <li>Introduced agent-to-agent messaging protocol</li>
                <li>Added support for collaborative problem-solving between agents</li>
                <li>Implemented memory sharing capabilities with configurable permissions</li>
              </ul>
              <div className="bg-muted p-4 rounded-md mt-3">
                <CodeBlock 
                  language="javascript"
                  code={`// Example: Setting up agent collaboration
const primaryAgent = await ruhClient.agents.create({
  name: "Research Coordinator",
  model: "gpt-4",
  tools: ["web-search", "document-analysis"]
});

const assistantAgent = await ruhClient.agents.create({
  name: "Data Processor",
  model: "gpt-3.5-turbo",
  tools: ["data-processing", "visualization"]
});

// Enable collaboration between agents
await ruhClient.agents.enableCollaboration({
  agents: [primaryAgent.id, assistantAgent.id],
  sharedMemory: true,
  messageProtocol: "structured"
});`}
                />
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg">New Integration Options</h3>
              <ul className="mt-2 space-y-2 list-disc pl-5">
                <li>Added native Slack integration</li>
                <li>Support for Notion databases as knowledge sources</li>
                <li>New API endpoints for custom integrations</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Previous Version Section */}
        <section>
          <div className="flex items-center gap-2 mb-4">
            <h2 id="version-1-4-2" className="h3 mb-0">Version 1.4.2</h2>
            <span className="text-muted-foreground text-sm">(April 15, 2025)</span>
          </div>

          <div className="border-l-2 border-muted pl-4 space-y-6">
            <div>
              <h3 className="font-semibold text-lg">Bug Fixes</h3>
              <ul className="mt-2 space-y-2 list-disc pl-5">
                <li>Fixed memory leaks in long-running workflow processes</li>
                <li>Resolved authentication token refresh issues</li>
                <li>Fixed inconsistent behavior in agent context switching</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-lg">Performance Improvements</h3>
              <ul className="mt-2 space-y-2 list-disc pl-5">
                <li>Optimized token usage calculations</li>
                <li>Improved response time for complex agent queries</li>
                <li>Reduced latency in cross-agent communications</li>
              </ul>
            </div>
          </div>
        </section>

        <section>
          <div className="flex items-center gap-2 mb-4">
            <h2 id="version-1-4-0" className="h3 mb-0">Version 1.4.0</h2>
            <span className="text-muted-foreground text-sm">(March 20, 2025)</span>
          </div>

          <div className="border-l-2 border-muted pl-4 space-y-6">
            <div>
              <h3 className="font-semibold text-lg">New Features</h3>
              <ul className="mt-2 space-y-2 list-disc pl-5">
                <li>Introduced agent memory persistence options</li>
                <li>Added new specialized research agent template</li>
                <li>Implemented advanced RAG capabilities for all agent types</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-lg">Documentation</h3>
              <ul className="mt-2 space-y-2 list-disc pl-5">
                <li>Complete API reference documentation</li>
                <li>New tutorials for common use cases</li>
                <li>Updated best practices for enterprise deployments</li>
              </ul>
            </div>
          </div>
        </section>

        <div className="flex justify-center pt-4">
          <a href="/docs/development/versions" className="text-primary hover:underline flex items-center">
            View all version history
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  );
}
