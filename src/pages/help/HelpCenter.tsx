
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";

export default function HelpCenter() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="container flex-1 py-10">
        <h1 className="text-4xl font-bold mb-6">Help Center</h1>
        <p className="text-xl text-muted-foreground mb-8">
          Find answers to common questions about Ruh.ai and get help with your project.
        </p>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="border rounded-lg p-6">
            <h2 className="text-2xl font-semibold mb-4">Getting Started</h2>
            <ul className="space-y-2">
              <li className="hover:underline cursor-pointer">How to create your first agent</li>
              <li className="hover:underline cursor-pointer">Understanding workflows</li>
              <li className="hover:underline cursor-pointer">API integration basics</li>
              <li className="hover:underline cursor-pointer">Platform overview</li>
            </ul>
          </div>
          <div className="border rounded-lg p-6">
            <h2 className="text-2xl font-semibold mb-4">Common Issues</h2>
            <ul className="space-y-2">
              <li className="hover:underline cursor-pointer">API rate limits</li>
              <li className="hover:underline cursor-pointer">Authentication problems</li>
              <li className="hover:underline cursor-pointer">Agent configuration</li>
              <li className="hover:underline cursor-pointer">Workflow debugging</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-12">
          <h2 className="text-2xl font-semibold mb-4">Still need help?</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="border rounded-lg p-6">
              <h3 className="text-xl font-medium mb-2">Contact Support</h3>
              <p className="text-muted-foreground mb-4">Our support team is available to assist you.</p>
              <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded">
                Contact Us
              </button>
            </div>
            <div className="border rounded-lg p-6">
              <h3 className="text-xl font-medium mb-2">Join Community</h3>
              <p className="text-muted-foreground mb-4">Get help from other Ruh.ai users.</p>
              <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded">
                Join Discord
              </button>
            </div>
            <div className="border rounded-lg p-6">
              <h3 className="text-xl font-medium mb-2">Documentation</h3>
              <p className="text-muted-foreground mb-4">Explore our comprehensive guides.</p>
              <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded">
                View Docs
              </button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
