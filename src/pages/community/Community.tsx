
import { Link } from "react-router-dom";

export default function Community() {
  return (
    <div className="container py-10">
      <div className="max-w-3xl mx-auto">
        <h1 className="h1">Community</h1>
        <p className="text-lg text-muted-foreground mt-4 mb-8">
          Join the Ruh.ai community to connect with other developers, share knowledge, and get help.
        </p>

        <div className="grid gap-6 md:grid-cols-2 mt-8">
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <h3 className="text-xl font-semibold mb-2">Discord Community</h3>
            <p className="text-muted-foreground mb-4">
              Join our active Discord server to chat with other developers, get help, and share your projects.
            </p>
            <a 
              href="https://discord.com/invite/ruhai" 
              target="_blank" 
              rel="noopener noreferrer" 
              className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90"
            >
              Join <PERSON>rd
            </a>
          </div>
          
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <h3 className="text-xl font-semibold mb-2">GitHub</h3>
            <p className="text-muted-foreground mb-4">
              Check out our open source projects, report issues, and contribute to the development of Ruh.ai.
            </p>
            <a 
              href="https://github.com/ruhai" 
              target="_blank" 
              rel="noopener noreferrer" 
              className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90"
            >
              Visit GitHub
            </a>
          </div>
        </div>

        <h2 className="h2 mt-12">Community Resources</h2>

        <div className="mt-6 space-y-6">
          <div className="rounded-lg border p-6">
            <h3 className="text-lg font-semibold mb-2">Community Showcases</h3>
            <p className="text-muted-foreground mb-4">
              Explore projects built by the community using Ruh.ai. Get inspired and see what's possible.
            </p>
            <Link 
              to="/community/showcases" 
              className="text-primary font-medium hover:underline"
            >
              Browse showcases →
            </Link>
          </div>

          <div className="rounded-lg border p-6">
            <h3 className="text-lg font-semibold mb-2">Contribution Guidelines</h3>
            <p className="text-muted-foreground mb-4">
              Learn how to contribute to Ruh.ai's documentation, codebase, and community resources.
            </p>
            <Link 
              to="/community/contribute" 
              className="text-primary font-medium hover:underline"
            >
              Read guidelines →
            </Link>
          </div>

          <div className="rounded-lg border p-6">
            <h3 className="text-lg font-semibold mb-2">Community Events</h3>
            <p className="text-muted-foreground mb-4">
              Join online and in-person events to learn more about Ruh.ai and connect with other developers.
            </p>
            <Link 
              to="/community/events" 
              className="text-primary font-medium hover:underline"
            >
              View upcoming events →
            </Link>
          </div>
        </div>

        <h2 className="h2 mt-12">Support Channels</h2>

        <div className="grid gap-6 md:grid-cols-2 mt-6">
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <h3 className="text-xl font-semibold mb-2">Technical Support</h3>
            <p className="text-muted-foreground mb-2">
              Get help with technical issues, bugs, and feature requests.
            </p>
            <ul className="space-y-1 mt-4">
              <li className="flex items-center text-sm text-muted-foreground">
                <span className="mr-2">•</span>
                <span>Discord <code>#support</code> channel</span>
              </li>
              <li className="flex items-center text-sm text-muted-foreground">
                <span className="mr-2">•</span>
                <span>GitHub Issues</span>
              </li>
              <li className="flex items-center text-sm text-muted-foreground">
                <span className="mr-2">•</span>
                <span>Email: <EMAIL></span>
              </li>
            </ul>
          </div>
          
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <h3 className="text-xl font-semibold mb-2">Community Help</h3>
            <p className="text-muted-foreground mb-2">
              Get help from other community members and Ruh.ai experts.
            </p>
            <ul className="space-y-1 mt-4">
              <li className="flex items-center text-sm text-muted-foreground">
                <span className="mr-2">•</span>
                <span>Discord <code>#community-help</code> channel</span>
              </li>
              <li className="flex items-center text-sm text-muted-foreground">
                <span className="mr-2">•</span>
                <span>Community Forums</span>
              </li>
              <li className="flex items-center text-sm text-muted-foreground">
                <span className="mr-2">•</span>
                <span>Stack Overflow <code>#ruhai</code> tag</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
