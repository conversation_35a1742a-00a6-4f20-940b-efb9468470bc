
import { useState } from "react";
import { CodeBlock } from "@/components/CodeBlock";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ChevronRight } from "lucide-react";
import { Link } from "react-router-dom";

type ApiLanguage = "curl" | "typescript" | "python";

export default function ApiReference() {
  const [selectedLanguage, setSelectedLanguage] = useState<ApiLanguage>("curl");

  return (
    <div className="container py-6 px-0 md:py-10">
      <div className="flex flex-col">
        <div className="flex items-center text-sm text-muted-foreground mb-4">
          <Link to="/api" className="hover:text-foreground">ENDPOINTS</Link>
          <ChevronRight className="h-4 w-4 mx-1" />
          <span>Workflow Execution</span>
        </div>
        
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight mb-6">Create Workflow Execution</h1>
          
          <div className="flex items-center mb-6">
            <span className="inline-flex items-center justify-center h-8 w-16 rounded bg-green-600 text-white text-sm font-medium mr-4">POST</span>
            <code className="text-sm font-mono bg-muted px-3 py-1.5 rounded">https://api.ruh.ai/v1/workflow-executions</code>
          </div>
          
          <p className="text-lg text-muted-foreground mb-8">
            Initiates a new workflow execution with the specified parameters. The workflow must exist and be accessible to your account.
          </p>

          <section id="path-parameters" className="mb-10">
            <h2 className="text-2xl font-semibold mb-4">Path parameters</h2>
            
            <div className="border rounded-lg overflow-hidden mb-4">
              <div className="grid grid-cols-[150px_1fr] gap-4 p-4 border-b">
                <div>
                  <code className="text-sm font-semibold">workflow_id</code>
                  <div className="text-xs text-muted-foreground mt-1">string</div>
                  <div className="text-xs font-medium text-red-500 mt-1">Required</div>
                </div>
                <div>
                  <p className="text-sm mb-2">The unique identifier of the workflow to execute.</p>
                </div>
              </div>
            </div>
          </section>

          <section id="headers" className="mb-10">
            <h2 className="text-2xl font-semibold mb-4">Headers</h2>
            
            <div className="border rounded-lg overflow-hidden mb-4">
              <div className="grid grid-cols-[150px_1fr] gap-4 p-4 border-b">
                <div>
                  <code className="text-sm font-semibold">Authorization</code>
                  <div className="text-xs text-muted-foreground mt-1">string</div>
                  <div className="text-xs font-medium text-red-500 mt-1">Required</div>
                </div>
                <div>
                  <p className="text-sm">Your API key for authentication. Format: <code>Bearer YOUR_API_KEY</code></p>
                </div>
              </div>
              <div className="grid grid-cols-[150px_1fr] gap-4 p-4">
                <div>
                  <code className="text-sm font-semibold">Content-Type</code>
                  <div className="text-xs text-muted-foreground mt-1">string</div>
                  <div className="text-xs font-medium text-red-500 mt-1">Required</div>
                </div>
                <div>
                  <p className="text-sm">Should be set to <code>application/json</code></p>
                </div>
              </div>
            </div>
          </section>

          <section id="request-body" className="mb-10">
            <h2 className="text-2xl font-semibold mb-4">Request body</h2>
            
            <div className="border rounded-lg overflow-hidden mb-4">
              <div className="grid grid-cols-[150px_1fr] gap-4 p-4 border-b">
                <div>
                  <code className="text-sm font-semibold">input_data</code>
                  <div className="text-xs text-muted-foreground mt-1">object</div>
                  <div className="text-xs font-medium text-muted-foreground mt-1">Optional</div>
                </div>
                <div>
                  <p className="text-sm mb-2">Input data to be processed by the workflow.</p>
                </div>
              </div>
              <div className="grid grid-cols-[150px_1fr] gap-4 p-4">
                <div>
                  <code className="text-sm font-semibold">options</code>
                  <div className="text-xs text-muted-foreground mt-1">object</div>
                  <div className="text-xs font-medium text-muted-foreground mt-1">Optional</div>
                </div>
                <div>
                  <p className="text-sm mb-2">Configuration options for the workflow execution.</p>
                  <div className="text-xs text-muted-foreground">
                    <p className="mb-1"><code>async</code> (boolean, optional): Whether to execute the workflow asynchronously. Defaults to false.</p>
                    <p><code>timeout</code> (number, optional): Timeout in seconds. Defaults to 30.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <Tabs 
              defaultValue="curl" 
              value={selectedLanguage} 
              onValueChange={(value) => setSelectedLanguage(value as ApiLanguage)}
              className="w-full mb-8"
            >
              <TabsList className="mb-2">
                <TabsTrigger value="curl">cURL</TabsTrigger>
                <TabsTrigger value="typescript">TypeScript</TabsTrigger>
                <TabsTrigger value="python">Python</TabsTrigger>
              </TabsList>
              <TabsContent value="curl">
                <CodeBlock 
                  language="bash"
                  method="POST"
                  endpoint="/v1/workflow-executions"
                  isExecutable
                  code={`curl https://api.ruh.ai/v1/workflow-executions \\
  -X POST \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "workflow_id": "wf_12345",
    "input_data": {
      "text": "Process this input",
      "parameters": {
        "key1": "value1",
        "key2": "value2"
      }
    },
    "options": {
      "async": true,
      "timeout": 60
    }
  }'`}
                />
              </TabsContent>
              <TabsContent value="typescript">
                <CodeBlock 
                  language="typescript"
                  method="POST"
                  endpoint="/v1/workflow-executions"
                  isExecutable
                  code={`import { RuhAI } from 'ruh-ai';

const client = new RuhAI({
  apiKey: "YOUR_API_KEY",
});

// Using the client
async function executeWorkflow() {
  const response = await client.workflows.execute({
    workflow_id: "wf_12345",
    input_data: {
      text: "Process this input",
      parameters: {
        key1: "value1",
        key2: "value2"
      }
    },
    options: {
      async: true,
      timeout: 60
    }
  });

  console.log(response);
}

// Or using fetch directly
async function executeWorkflowWithFetch() {
  const response = await fetch('https://api.ruh.ai/v1/workflow-executions', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer YOUR_API_KEY',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      workflow_id: "wf_12345",
      input_data: {
        text: "Process this input",
        parameters: {
          key1: "value1",
          key2: "value2"
        }
      },
      options: {
        async: true,
        timeout: 60
      }
    })
  });

  const data = await response.json();
  console.log(data);
}`}
                />
              </TabsContent>
              <TabsContent value="python">
                <CodeBlock 
                  language="python"
                  method="POST"
                  endpoint="/v1/workflow-executions"
                  isExecutable
                  code={`import requests
import json

url = "https://api.ruh.ai/v1/workflow-executions"
headers = {
    "Authorization": "Bearer YOUR_API_KEY",
    "Content-Type": "application/json"
}
payload = {
    "workflow_id": "wf_12345",
    "input_data": {
        "text": "Process this input",
        "parameters": {
            "key1": "value1",
            "key2": "value2"
        }
    },
    "options": {
        "async": True,
        "timeout": 60
    }
}

response = requests.post(url, headers=headers, data=json.dumps(payload))

print(response.status_code)
print(response.json())`}
                />
              </TabsContent>
            </Tabs>
          </section>

          <section id="response" className="mb-10">
            <h2 className="text-2xl font-semibold mb-4">Response</h2>
            
            <CodeBlock 
              language="json"
              statusCode={200}
              responseType="success"
              code={`{
  "execution_id": "exec_67890",
  "status": "running",
  "created_at": "2025-05-06T14:30:20Z",
  "estimated_completion": "2025-05-06T14:31:20Z",
  "results_url": "https://api.ruh.ai/v1/workflow-executions/exec_67890/results"
}`}
            />
          </section>

          <section id="errors" className="mb-10">
            <h2 className="text-2xl font-semibold mb-4">Errors</h2>
            
            <div className="border rounded-lg overflow-hidden mb-4">
              <div className="grid grid-cols-[100px_1fr] gap-4 p-4 border-b">
                <div className="text-sm font-medium">400</div>
                <div className="text-sm">Bad Request - Invalid request body or parameters</div>
              </div>
              <div className="grid grid-cols-[100px_1fr] gap-4 p-4 border-b">
                <div className="text-sm font-medium">401</div>
                <div className="text-sm">Unauthorized - Invalid API key</div>
              </div>
              <div className="grid grid-cols-[100px_1fr] gap-4 p-4 border-b">
                <div className="text-sm font-medium">404</div>
                <div className="text-sm">Not Found - Workflow not found</div>
              </div>
              <div className="grid grid-cols-[100px_1fr] gap-4 p-4">
                <div className="text-sm font-medium">429</div>
                <div className="text-sm">Too Many Requests - Rate limit exceeded</div>
              </div>
            </div>

            <CodeBlock 
              language="json"
              statusCode={400}
              responseType="error"
              code={`{
  "error": {
    "code": "invalid_request",
    "message": "Invalid request parameters",
    "details": [
      {
        "field": "workflow_id",
        "issue": "This field is required"
      }
    ]
  }
}`}
            />
          </section>

          <section id="try-it-out" className="mb-10">
            <h2 className="text-2xl font-semibold mb-4">Try it out</h2>
            
            <div className="border rounded-lg p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="workflow-id">
                  Workflow ID
                </label>
                <input 
                  type="text"
                  id="workflow-id"
                  className="w-full p-2 border rounded text-sm"
                  placeholder="e.g. wf_12345"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="api-key">
                  API Key
                </label>
                <input 
                  type="text"
                  id="api-key"
                  className="w-full p-2 border rounded text-sm"
                  placeholder="YOUR_API_KEY"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="input-data">
                  Input Data (JSON)
                </label>
                <textarea 
                  id="input-data"
                  rows={5}
                  className="w-full p-2 border rounded text-sm font-mono"
                  placeholder='{"text": "Process this input", "parameters": {"key1": "value1"}}'
                ></textarea>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="options">
                  Options (JSON)
                </label>
                <textarea 
                  id="options"
                  rows={2}
                  className="w-full p-2 border rounded text-sm font-mono"
                  placeholder='{"async": true, "timeout": 60}'
                ></textarea>
              </div>
              
              <button 
                className="px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 rounded"
              >
                Send Request
              </button>
              
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-2">Response</h3>
                <div className="bg-muted p-4 rounded">
                  <pre className="text-sm font-mono whitespace-pre-wrap">
                    Response will appear here...
                  </pre>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
