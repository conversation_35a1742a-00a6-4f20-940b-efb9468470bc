
import { Head<PERSON> } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { ArrowRight, Book, Code, Users } from "lucide-react";

export default function Index() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-br from-background via-background to-accent/20">
          <div className="container px-4 md:px-6 space-y-10 xl:space-y-16">
            <div className="grid gap-4 px-10 md:px-6 lg:grid-cols-2 lg:gap-10">
              <div className="space-y-4">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                  Ruh.ai Documentation
                </h1>
                <p className="max-w-[600px] text-muted-foreground md:text-xl">
                  Comprehensive guides and references for building intelligent AI workflows with Ruh.ai
                </p>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Link to="/docs">
                    <Button size="lg">
                      Get Started
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                  <Link to="/api">
                    <Button variant="outline" size="lg">
                      API Reference
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="hidden md:block relative">
                <div className="absolute -top-16 -right-20 size-[400px] bg-primary/10 rounded-full blur-3xl" />
                <div className="relative">
                  <div className="border shadow-xl rounded-lg overflow-hidden bg-accent/20 backdrop-blur-sm">
                    <pre className="p-4 overflow-x-auto text-xs md:text-sm text-foreground/90 font-mono">
{`// Example: Creating an AI agent
const ruhClient = new RuhAI(API_KEY);

const myAgent = await ruhClient.agents.create({
  name: "Customer Support Assistant",
  description: "Handles customer inquiries",
  model: "gpt-4",
  tools: ["knowledge-base", "ticket-system"],
  memory: {
    conversationSteps: 10
  }
});

// The agent can now be accessed via its ID
console.log(\`Agent created: \${myAgent.id}\`);`}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Links */}
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Explore Ruh.ai Documentation
                </h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Find the information you need to build powerful AI-driven experiences.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-3">
              <Link to="/docs" className="group relative overflow-hidden rounded-lg border bg-background shadow-md transition-all hover:-translate-y-1 hover:shadow-xl">
                <div className="p-6">
                  <div className="flex items-center justify-center h-12 w-12 rounded-full bg-primary/10 mb-4">
                    <Book className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold transition-colors group-hover:text-primary">Documentation</h3>
                  <p className="text-muted-foreground mt-2">
                    Learn how to use Ruh.ai with our comprehensive guides and tutorials.
                  </p>
                </div>
              </Link>
              <Link to="/api" className="group relative overflow-hidden rounded-lg border bg-background shadow-md transition-all hover:-translate-y-1 hover:shadow-xl">
                <div className="p-6">
                  <div className="flex items-center justify-center h-12 w-12 rounded-full bg-primary/10 mb-4">
                    <Code className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold transition-colors group-hover:text-primary">API Reference</h3>
                  <p className="text-muted-foreground mt-2">
                    Technical reference for the Ruh.ai API including endpoints and examples.
                  </p>
                </div>
              </Link>
              <Link to="/community" className="group relative overflow-hidden rounded-lg border bg-background shadow-md transition-all hover:-translate-y-1 hover:shadow-xl">
                <div className="p-6">
                  <div className="flex items-center justify-center h-12 w-12 rounded-full bg-primary/10 mb-4">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold transition-colors group-hover:text-primary">Community</h3>
                  <p className="text-muted-foreground mt-2">
                    Join our community, get help, and share your Ruh.ai projects.
                  </p>
                </div>
              </Link>
            </div>
          </div>
        </section>

        {/* Feature sections */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-accent/50">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 xl:grid-cols-3">
              <div className="space-y-4">
                <div className="inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm">AI Agents</div>
                <h3 className="text-2xl font-bold">Intelligent AI Agents</h3>
                <p className="text-muted-foreground">
                  Create AI agents that can perform tasks, make decisions, and interact with users and other systems.
                </p>
                <Link 
                  to="/docs/agents/intro" 
                  className="inline-flex items-center text-primary"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
              <div className="space-y-4">
                <div className="inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm">Workflows</div>
                <h3 className="text-2xl font-bold">Powerful Workflows</h3>
                <p className="text-muted-foreground">
                  Design and build complex workflows that orchestrate AI agents and integrate with your existing systems.
                </p>
                <Link 
                  to="/docs/workflows/intro" 
                  className="inline-flex items-center text-primary"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
              <div className="space-y-4">
                <div className="inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm">Tools</div>
                <h3 className="text-2xl font-bold">Extensible Tools</h3>
                <p className="text-muted-foreground">
                  Connect your agents to external systems and data sources with our extensive tool ecosystem.
                </p>
                <Link 
                  to="/docs/tools/using-tools" 
                  className="inline-flex items-center text-primary"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
