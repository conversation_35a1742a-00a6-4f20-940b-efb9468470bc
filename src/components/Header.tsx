
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Menu, X, HelpCircle } from "lucide-react";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Link } from "react-router-dom";
import Logo from '@/assets/logo.svg';

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-6">
          <Link to="/" className="flex items-center space-x-2">
            {/* <span className="font-bold text-xl bg-gradient-to-r from-blue-500 to-purple-500 text-transparent bg-clip-text">Ruh.ai</span> */}
            <img src={Logo} alt="Ruh.ai Logo" className="h-6 w-15" />
          </Link>
          <nav className="hidden md:flex items-center gap-6">
            <Link to="/docs" className="text-sm font-medium hover:text-primary transition-colors">
              Documentation
            </Link>
            <Link to="/api" className="text-sm font-medium hover:text-primary transition-colors">
              API Reference
            </Link>
            <Link to="/community" className="text-sm font-medium hover:text-primary transition-colors">
              Community
            </Link>
          </nav>
        </div>
        <div className="flex items-center gap-4">
          <div className="hidden sm:flex relative w-60">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search documentation..."
              className="pl-8 w-full"
            />
          </div>
          
          <Link to="/help-center" className="hidden md:flex items-center text-sm font-medium hover:text-primary transition-colors">
            <HelpCircle className="h-4 w-4 mr-1" />
            Help Center
          </Link>
          
          <div className="hidden md:flex items-center gap-2">
            <Button variant="ghost" size="sm" asChild>
              <Link to="/login">Log in</Link>
            </Button>
            <Button size="sm" asChild>
              <Link to="/signup">Sign up</Link>
            </Button>
          </div>
          
          <ThemeToggle />
          <Button
            variant="outline"
            size="icon"
            className="md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
      </div>
      {mobileMenuOpen && (
        <div className="md:hidden border-t p-4 bg-background">
          <nav className="grid gap-2">
            <Link
              to="/docs"
              className="flex items-center gap-2 text-sm font-medium p-2 rounded-md hover:bg-accent"
            >
              Documentation
            </Link>
            <Link
              to="/api"
              className="flex items-center gap-2 text-sm font-medium p-2 rounded-md hover:bg-accent"
            >
              API Reference
            </Link>
            <Link
              to="/community"
              className="flex items-center gap-2 text-sm font-medium p-2 rounded-md hover:bg-accent"
            >
              Community
            </Link>
            <Link
              to="/help-center"
              className="flex items-center gap-2 text-sm font-medium p-2 rounded-md hover:bg-accent"
            >
              <HelpCircle className="h-4 w-4" />
              Help Center
            </Link>
            <Link
              to="/login"
              className="flex items-center gap-2 text-sm font-medium p-2 rounded-md hover:bg-accent"
            >
              Log in
            </Link>
            <Link
              to="/signup"
              className="flex items-center gap-2 text-sm font-medium p-2 rounded-md hover:bg-accent"
            >
              Sign up
            </Link>
            <div className="relative mt-2">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search documentation..."
                className="pl-8 w-full"
              />
            </div>
          </nav>
        </div>
      )}
    </header>
  );
}
