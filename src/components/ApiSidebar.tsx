
import { useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

type ApiCategory = {
  title: string;
  id: string;
  items: ApiItem[];
};

type ApiItem = {
  title: string;
  id: string;
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH" | "WS" | "STREAM";
};

const apiCategories: ApiCategory[] = [
  {
    title: "API REFERENCE",
    id: "api-reference-main",
    items: [
      { title: "Introduction", id: "introduction" },
      { title: "Authentication", id: "authentication" },
      { title: "Rate Limits", id: "rate-limits" }
    ]
  },
  {
    title: "ENDPOINTS",
    id: "endpoints-main",
    items: []
  },
  {
    title: "Workflow Execution",
    id: "workflow-execution",
    items: [
      { title: "List Executions", id: "list-executions", method: "GET" },
      { title: "Create Execution", id: "create-execution", method: "POST" },
      { title: "Get Execution", id: "get-execution", method: "GET" },
      { title: "Cancel Execution", id: "cancel-execution", method: "POST" }
    ]
  },
  {
    title: "Agent Endpoints",
    id: "agents-section",
    items: [
      { title: "List Agents", id: "list-agents", method: "GET" },
      { title: "Create Agent", id: "create-agent", method: "POST" },
      { title: "Get Agent", id: "get-agent", method: "GET" },
      { title: "Update Agent", id: "update-agent", method: "PUT" },
      { title: "Delete Agent", id: "delete-agent", method: "DELETE" }
    ]
  },
  {
    title: "Tool Endpoints",
    id: "tools-section",
    items: [
      { title: "List Available Tools", id: "list-tools", method: "GET" },
      { title: "Get Tool", id: "get-tool", method: "GET" },
      { title: "Tool Configurations", id: "tool-configs", method: "GET" }
    ]
  },
  {
    title: "Error Handling",
    id: "error-section",
    items: [
      { title: "Error Codes", id: "error-codes" }
    ]
  }
];

export function ApiSidebar() {
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>(
    apiCategories.reduce((acc, category) => ({ ...acc, [category.id]: true }), {})
  );

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const getMethodColor = (method?: string) => {
    switch (method) {
      case "GET":
        return "bg-blue-600";
      case "POST":
        return "bg-green-600";
      case "PUT":
        return "bg-yellow-600";
      case "DELETE":
        return "bg-red-600";
      case "PATCH":
        return "bg-purple-600";
      case "WS":
        return "bg-emerald-600";
      case "STREAM":
        return "bg-cyan-600";
      default:
        return "bg-gray-600";
    }
  };

  const getMethodLabel = (method?: string) => {
    if (method === "WS") return "WSS";
    return method;
  };

  return (
    <div className="w-full md:w-64 md:shrink-0 md:mr-8 mb-6 md:mb-0 border-r">
      <div className="sticky top-20 overflow-y-auto max-h-[calc(100vh-100px)] pb-4">
        <nav className="space-y-1 px-4">
          {apiCategories.map((category) => (
            <div key={category.id} className="mb-6">
              {category.id !== "endpoints-main" && (
                <button
                  onClick={() => toggleCategory(category.id)}
                  className="flex items-center w-full text-left py-1.5 text-xs font-semibold text-muted-foreground"
                >
                  {expandedCategories[category.id] ? (
                    <ChevronDown className="h-3 w-3 mr-1" />
                  ) : (
                    <ChevronRight className="h-3 w-3 mr-1" />
                  )}
                  {category.title}
                </button>
              )}
              
              {category.id === "endpoints-main" && (
                <div className="text-xs font-semibold text-muted-foreground py-1.5">
                  {category.title}
                </div>
              )}
              
              {expandedCategories[category.id] && category.items.length > 0 && (
                <ul className={cn("mt-1 space-y-1", category.id === "api-reference-main" ? "" : "pl-2")}>
                  {category.items.map((item) => (
                    <li key={item.id}>
                      <button
                        onClick={() => scrollToSection(item.id)}
                        className="flex items-center w-full text-left px-2 py-1 text-sm rounded-md hover:bg-muted"
                      >
                        {item.method && (
                          <span className={cn("inline-flex items-center justify-center h-5 w-12 rounded text-white text-xs font-medium mr-2", getMethodColor(item.method))}>
                            {getMethodLabel(item.method)}
                          </span>
                        )}
                        <span className="text-sm">{item.title}</span>
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          ))}
        </nav>
      </div>
    </div>
  );
}
