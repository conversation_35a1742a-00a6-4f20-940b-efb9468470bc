import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { ChevronDown, Calendar, Star, GitBranch } from "lucide-react";

type SidebarItem = {
  title: string;
  path?: string;
  children?: SidebarItem[];
  icon?: React.ReactNode;
};

const sidebarItems: SidebarItem[] = [
  {
    title: "Getting Started",
    path: "/docs",
  },
  {
    title: "AI Agents",
    children: [
      { title: "Introduction to AI Agents", path: "/docs/agents/intro" },
      { title: "Creating & Configuring Agents", path: "/docs/agents/creating" },
      { title: "Managing Agent Lifecycles", path: "/docs/agents/lifecycle" },
      { title: "Agent Capabilities & Limitations", path: "/docs/agents/capabilities" },
      { title: "Best Practices", path: "/docs/agents/best-practices" },
    ],
  },
  {
    title: "Workflows",
    children: [
      { title: "Understanding Workflows", path: "/docs/workflows/intro" },
      { title: "Designing & Building Workflows", path: "/docs/workflows/designing" },
      { title: "Triggers & Actions", path: "/docs/workflows/triggers-actions" },
      { title: "Workflow Variables", path: "/docs/workflows/variables" },
      { title: "Monitoring Workflows", path: "/docs/workflows/monitoring" },
      { title: "Workflow Examples", path: "/docs/workflows/examples" },
    ],
  },
  {
    title: "Tools",
    children: [
      { title: "Using Tools with Agents", path: "/docs/tools/using-tools" },
      { title: "Built-in Tool Directory", path: "/docs/tools/built-in" },
      { title: "External Integrations", path: "/docs/tools/integrations" },
      { title: "Creating Custom Tools", path: "/docs/tools/custom" },
      { title: "Tool Security", path: "/docs/tools/security" },
    ],
  },
  {
    title: "Advanced Topics",
    children: [
      { title: "Security & Authentication", path: "/docs/advanced/security" },
      { title: "Rate Limiting & Quotas", path: "/docs/advanced/rate-limits" },
      { title: "Deployment Options", path: "/docs/advanced/deployment" },
      { title: "Troubleshooting", path: "/docs/advanced/troubleshooting" },
    ],
  },
  {
    title: "Development",
    children: [
      { title: "What's New", path: "/docs/development/whats-new", icon: <Star className="h-4 w-4" /> },
      { title: "Roadmap", path: "/docs/development/roadmap", icon: <GitBranch className="h-4 w-4" /> },
      { title: "Version History", path: "/docs/development/versions", icon: <Calendar className="h-4 w-4" /> },
    ],
  },
];

function SidebarLink({
  item,
  depth = 0,
}: {
  item: SidebarItem;
  depth?: number;
}) {
  const location = useLocation();
  const [isExpanded, setIsExpanded] = useState(true);
  const isActive = item.path === location.pathname;
  const hasChildren = !!item.children?.length;
  
  // Only expand sections with children if we're not at the top level
  const isCurrentlyExpanded = 
    depth === 0 ? true : isExpanded;

  return (
    <li>
      <div
        className={cn(
          "flex items-center justify-between py-2",
          depth > 0 && "ml-4",
          isActive && "text-primary font-medium"
        )}
      >
        {item.path ? (
          <Link
            to={item.path}
            className={cn(
              "flex-1 hover:text-primary transition-colors flex items-center gap-2",
              isActive && "text-primary font-medium"
            )}
          >
            {item.icon && item.icon}
            <span>{item.title}</span>
          </Link>
        ) : (
          <div className="flex-1 font-medium">{item.title}</div>
        )}
        {hasChildren && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 rounded-md hover:bg-accent"
            aria-label={`${isExpanded ? "Collapse" : "Expand"} ${item.title} section`}
          >
            <ChevronDown
              className={cn(
                "h-4 w-4 text-muted-foreground transition-transform",
                isExpanded && "transform rotate-180"
              )}
            />
          </button>
        )}
      </div>
      {hasChildren && isCurrentlyExpanded && (
        <ul className="text-sm text-muted-foreground space-y-1 pt-1">
          {item.children?.map((child) => (
            <SidebarLink key={child.title} item={child} depth={depth + 1} />
          ))}
        </ul>
      )}
    </li>
  );
}

export function DocsSidebar() {
  return (
    <aside className="flex flex-col sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto py-6 pr-2 hidden md:block">
      <nav className="grid gap-4">
        <ul className="grid gap-2 text-sm">
          {sidebarItems.map((item) => (
            <SidebarLink key={item.title} item={item} />
          ))}
        </ul>
      </nav>
    </aside>
  );
}
