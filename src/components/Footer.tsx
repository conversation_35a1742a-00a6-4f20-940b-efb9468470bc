
import { Link } from "react-router-dom";

export function Footer() {
  return (
    <footer className="border-t py-8 mt-20">
      <div className="container flex flex-col md:flex-row items-start justify-between gap-8">
        <div className="space-y-4">
          <Link to="/" className="flex items-center space-x-2">
            <span className="font-bold text-xl bg-gradient-to-r from-blue-500 to-purple-500 text-transparent bg-clip-text">Ruh.ai</span>
          </Link>
          <p className="text-sm text-muted-foreground">
            Advanced AI platform for building intelligent workflows
          </p>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-10">
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Resources</h4>
            <ul className="grid gap-2 text-sm">
              <li>
                <Link to="/docs" className="text-muted-foreground hover:text-foreground transition-colors">
                  Documentation
                </Link>
              </li>
              <li>
                <Link to="/api" className="text-muted-foreground hover:text-foreground transition-colors">
                  API Reference
                </Link>
              </li>
              <li>
                <Link to="/community" className="text-muted-foreground hover:text-foreground transition-colors">
                  Community
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Company</h4>
            <ul className="grid gap-2 text-sm">
              <li>
                <Link to="/about" className="text-muted-foreground hover:text-foreground transition-colors">
                  About
                </Link>
              </li>
              <li>
                <Link to="/blog" className="text-muted-foreground hover:text-foreground transition-colors">
                  Blog
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-muted-foreground hover:text-foreground transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Legal</h4>
            <ul className="grid gap-2 text-sm">
              <li>
                <Link to="/privacy" className="text-muted-foreground hover:text-foreground transition-colors">
                  Privacy
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-muted-foreground hover:text-foreground transition-colors">
                  Terms
                </Link>
              </li>
              <li>
                <Link to="/support" className="text-muted-foreground hover:text-foreground transition-colors">
                  Help
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div className="container mt-8 pt-4 border-t">
        <p className="text-xs text-muted-foreground">
          &copy; {new Date().getFullYear()} Ruh.ai. All rights reserved.
        </p>
      </div>
    </footer>
  );
}
