
import { useState } from "react";
import { <PERSON><PERSON> } from "./ui/button";
import { Check, Copy, PlayCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "./ui/badge";

interface CodeBlockProps {
  code: string;
  language?: string;
  endpoint?: string;
  method?: string;
  isEditable?: boolean;
  isExecutable?: boolean;
  className?: string;
  statusCode?: number;
  responseType?: "success" | "error" | "loading";
}

export function CodeBlock({ 
  code, 
  language = "bash", 
  endpoint,
  method,
  isEditable = false,
  isExecutable = false,
  className,
  statusCode,
  responseType
}: CodeBlockProps) {
  const [copied, setCopied] = useState(false);
  const [executing, setExecuting] = useState(false);
  const [codeContent, setCodeContent] = useState(code);

  const handleCopy = () => {
    navigator.clipboard.writeText(codeContent);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  const handleExecute = () => {
    setExecuting(true);
    // Simulate API call
    setTimeout(() => {
      setExecuting(false);
    }, 1500);
  };
  
  const methodColors: Record<string, string> = {
    GET: "bg-blue-600",
    POST: "bg-green-600",
    PUT: "bg-yellow-600",
    DELETE: "bg-red-600",
    PATCH: "bg-purple-600"
  };

  const statusColors: Record<string, string> = {
    success: "text-green-500",
    error: "text-red-500",
    loading: "text-yellow-500"
  };

  return (
    <div className={cn("relative group rounded-md border overflow-hidden", className)}>
      <div className="flex justify-between items-center bg-muted px-4 py-2">
        {endpoint ? (
          <div className="flex items-center space-x-2">
            {method && (
              <span className={cn("inline-block px-2 py-1 text-xs font-semibold rounded text-white", methodColors[method] || "bg-gray-600")}>
                {method}
              </span>
            )}
            <code className="text-xs font-mono">{endpoint}</code>
          </div>
        ) : (
          <div className="text-xs font-medium text-muted-foreground">
            {language.toUpperCase()}
          </div>
        )}

        {statusCode && (
          <Badge className={cn(
            statusCode >= 200 && statusCode < 300 ? "bg-green-500/10 text-green-500 hover:bg-green-500/20" :
            statusCode >= 400 ? "bg-red-500/10 text-red-500 hover:bg-red-500/20" :
            "bg-yellow-500/10 text-yellow-500 hover:bg-yellow-500/20"
          )}>
            {statusCode} {responseType === "success" ? "Successful" : responseType === "error" ? "Error" : ""}
          </Badge>
        )}

        <div className="flex space-x-2">
          {isExecutable && (
            <Button
              size="sm"
              variant="outline"
              className="h-8 px-3 text-xs"
              onClick={handleExecute}
              disabled={executing}
            >
              {executing ? "Running..." : (
                <>
                  <PlayCircle className="h-3.5 w-3.5 mr-1" />
                  Try it
                </>
              )}
            </Button>
          )}
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0 hover:bg-slate-100 dark:hover:bg-slate-800"
            onClick={handleCopy}
          >
            {copied ? (
              <Check className="h-4 w-4" />
            ) : (
              <Copy className="h-4 w-4" />
            )}
            <span className="sr-only">Copy code</span>
          </Button>
        </div>
      </div>
      {isEditable ? (
        <textarea
          className="p-4 w-full h-auto min-h-[100px] font-mono text-sm bg-background focus:outline-none"
          value={codeContent}
          onChange={(e) => setCodeContent(e.target.value)}
        />
      ) : (
        <pre className="p-4 overflow-x-auto text-sm font-mono">
          <code className={language ? `language-${language}` : undefined}>{codeContent}</code>
        </pre>
      )}
    </div>
  );
}
