
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222 47% 11%;
    
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    
    --primary: 235 85% 60%;
    --primary-foreground: 210 40% 98%;
    
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;
    
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 222 47% 11%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 70%;
    
    --radius: 0.5rem;
    
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222 47% 11%;
    --sidebar-primary: 235 85% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 222 47% 11%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 222 84% 70%;
    
    --code-background: 212 30% 96%;
    --code-foreground: 222 47% 11%;
  }
  
  .dark {
    --background: 222 47% 9%;
    --foreground: 210 40% 98%;
    
    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;
    
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;
    
    --primary: 235 85% 60%;
    --primary-foreground: 210 40% 98%;
    
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
    
    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;
    
    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 224 76% 48%;
    
    --sidebar-background: 222 47% 9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 235 85% 60%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 33% 20%;
    --sidebar-ring: 224 76% 48%;
    
    --code-background: 222 47% 13%;
    --code-foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  code, pre {
    @apply font-mono text-sm;
  }
  
  pre {
    @apply bg-code text-code-foreground p-4 rounded-md overflow-auto;
  }
  
  code {
    @apply bg-code text-code-foreground px-1.5 py-0.5 rounded;
  }
  
  pre code {
    @apply bg-transparent p-0 text-code-foreground;
  }
}

@layer components {
  .docs-container {
    @apply grid grid-cols-1 md:grid-cols-[250px_1fr] lg:grid-cols-[300px_1fr] xl:grid-cols-[300px_1fr_200px] gap-8;
  }
  
  .h1 {
    @apply scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl;
  }
  
  .h2 {
    @apply scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight transition-colors first:mt-0 mt-10;
  }
  
  .h3 {
    @apply scroll-m-20 text-2xl font-semibold tracking-tight mt-8;
  }
  
  .h4 {
    @apply scroll-m-20 text-xl font-semibold tracking-tight mt-8;
  }
  
  .copy-button {
    @apply absolute right-4 top-4 opacity-0 group-hover:opacity-100 transition-opacity;
  }
}
