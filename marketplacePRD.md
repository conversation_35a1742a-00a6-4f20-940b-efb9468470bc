**Product Requirements Document (PRD) - RUH Marketplace**

**Introduction**

*   **1.1 Purpose:**The RUH Marketplace is a platform designed to enable the discovery, sharing, and reuse of AI Agents, Workflows, and Model Context Protocol (MCP) Tools. It facilitates a collaborative ecosystem where developers can browse, test, and adopt AI components built by the community or ruh.ai itself.
    

*   **1.2 Goals:**
    
    *   Enable Users to discover high-quality reusable components (Agents, Workflows, MCPs).
        
    *   Provide detailed insights into items with metadata, usage stats, and author information.
        
    *   Support a seamless experience from browsing to integration.
        
    *   Drive engagement by making publishing and discovery easy and intuitive.
        
    *   Offer powerful filtering, search, and sorting to reduce friction in discovery.
        
*   **1.3 Target Audience:**
    
    *   Developers are building AI-powered applications.
        
    *   Platform architects and solution consultants.
        
    *   Admins evaluating tools for operational use.
        
    *   Users who have basic knowledge.
        
*   **1.4 Success Metrics:**
    
    *   Number of marketplace items published.
        
    *   Number of items added to user projects (downloads).
        
    *   Item interaction rate (views, shares, usage).
        

**Product Overview**

*   **2.1 Vision:**To become the go-to ecosystem for reusable and composable AI agents, automation workflows, and MCP-integrated tools within the ruh.ai platform.
    

*   **2.2 Key Features:**
    
    *   Comprehensive search functionality for all marketplace items.
        
    *   Primary "Type" filter (All, MCPs, Agents, Workflows) for browsing specific item categories.
        
    *   Filtering by categories (via a dedicated sidebar), tags (via a filter button/modal), and author.
        
    *   Marketplace item cards with rich previews including title, author, type, rating, and download count.
        
    *   Detailed item pages featuring a tabbed interface ("Overview", "Tools", "Integration") with comprehensive metadata, related items, and clear actions.
        
    *   Sorting functionality for item listings.
        
    *   Responsive, accessible, and mobile-friendly UI.
        

**Functional Requirements**

*   **3.1 Browsing and Discovery**
    
    *   Users can filter items by type ("All", "MCPs", "Agents", "Workflows") using a primary filter control displayed prominently on the listing page.
        
    *   A "Categories" sidebar shall list available item categories with a count of items in each. Users can click a category to filter the item list. An "All" option removes the category filter.
        
    *   Items are shown in a responsive grid layout.
        
    *   Secondary visual tabs/labels (e.g., "All Items", "MCPs", "Agents", "Workflows") above the item grid shall reflect the currently active "Type" filter.
        
    *   Implement pagination with a configurable page size for item listings.
        
    *   Skeleton loaders should display during content fetch for item cards.
        
*   **3.2 Search and Filtering**
    
    *   Full-text search across item name, description, and tags, initiated via a search bar and "Search" button.
        
    *   Filters include:
        
        *   **Type:** Selectable via primary filter controls (All, MCPs, Agents, Workflows).
            
        *   **Category:** Selectable from the "Categories" sidebar.
            
        *   **Tags:** Selectable via a dedicated "Tags" filter button that opens a selection mechanism.
            
    *   Sorting by: average rating, downloads, creation date, last updated date.
        
    *   Clicking a category name (from sidebar), author name, or tag (from card/detail page/tag filter) applies the corresponding filter.
        
*   **3.3 Item Cards**
    
    *   Display item image (or default placeholder).
        
    *   Display item title and author name.
        
    *   Visual type indicator (e.g., "MCP", "AGENT", "WORKFLOW" badge).
        
    *   Show download count.
        
    *   Show average rating (e.g., "4.8" with a star icon).
        
    *   Display a badge for the item's primary category.
        
*   **3.4 Item Details Page**
    
    *   The page shall feature a tabbed interface with three main sections: "Overview", "Tools", and "Integration", to organize detailed information effectively. A persistent sidebar will display key metadata and actions.
        
    *   **Sidebar Content:**
        
        *   Primary action button (e.g., "Add to RUH").
            
        *   Author information (name, linkable to filter by author).
            
        *   Released date (creation timestamp).
            
        *   Total downloads count.
            
        *   Primary category (linkable to filter by category).
            
        *   Associated tags (each linkable to filter by tag).
            
        *   List of "Related Items" (by tag or category).
            
    *   **Tab: Overview**
        
        *   Displays the item's full title and a comprehensive description (rich text).
            
        *   Includes a "Features" section listing key capabilities as bullet points.
            
        *   May show item-specific general info like model used (Agent), version (Workflow), or protocol status (MCP).
            
    *   **Tab: Tools** (Content varies based on item type)
        
        *   **For MCP Tools ("Included Tools"):** Lists other marketplace items (Agents, Workflows, or other MCPs) that this MCP Tool utilizes or integrates with, each with a link to its marketplace page.
            
        *   **For Agents ("Capabilities" / "Configured Components"):** Lists the tools, MCPs, and Workflows the Agent is configured to use, each with a link to its marketplace page. May also provide a summary of the Agent's knowledge base sources.
            
        *   **For Workflows ("Components" / "Orchestration Details"):** Describes or lists the key services, agents, or MCPs it orchestrates. If these are marketplace items, they will link to their respective pages.
            
    *   **Tab: Integration** (Content varies based on item type)
        
        *   **For MCP Tools:**
            
            *   Information on obtaining/generating necessary API keys.
                
            *   Code examples in various languages (e.g., TypeScript, Python, cURL) for interacting with the MCP, with copy-to-clipboard.
                
            *   A link/button to "Open on Platform" for direct interaction or testing.
                
        *   **For Agents:**
            
            *   Information on obtaining/generating necessary API keys.
                
            *   Code examples for programmatic interaction, with copy-to-clipboard.
                
            *   "A2A Protocol Integration" section: Displaying the Agent's shareable "Card Link" and its "Task Endpoint" for agent-to-agent communication, both with copy-to-clipboard.
                
            *   A link/button to "Open on Platform" for testing or direct interaction (e.g., chat interface).
                
        *   **For Workflows:**
            
            *   Information on obtaining/generating necessary API keys.
                
            *   Code examples for triggering the workflow programmatically, with copy-to-clipboard.
                
            *   "Workflow Integration" section: Displaying the specific "Workflow Endpoint" URL for triggering the workflow, with copy-to-clipboard.
                
            *   Optional: Example input/output schemas for the workflow.
                
            *   A link/button to "Open on Platform" for managing or monitoring the workflow.
                
*   **3.5 Item Actions**
    
    *   "View Details" action on item cards navigates to the full item detail page.
        
    *   "Add to Workspace" / "Download" action (prominently on detail page sidebar and potentially on item cards) adds the item to the user's workspace/project and increments the download count.
        
    *   "Share" link functionality for public marketplace items to easily share a link to the item's detail page.
        
    *   "Copy code snippet" / "Copy API endpoint" / "Copy A2A link" actions within the Integration tab of the detail page.
        
*   **3.7 UI/UX Features**
    
    *   Responsive layout for desktop, tablet, and mobile devices.
        
    *   Support for Light and Dark mode themes.
        
    *   User-friendly empty state messages (e.g., for no search results, empty categories).
        
    *   Clear loading indicators during asynchronous data fetches.
        

**Design Considerations**

*   Minimalist, card-first design aesthetic.
    
*   Emphasis on readability and enabling users to quickly scan for information.
    
*   Consistent layout, iconography, and interaction patterns across different item types and pages.
    
*   Previews and information on cards (e.g., agent avatar, workflow execution count, MCP status) must be informative and accurate.
    

**MVP Criteria**

*   Core "Type" filter control (All, MCPs, Agents, Workflows) with corresponding item listing.
    
*   Search functionality.
    
*   Filtering by category (via sidebar with counts) and tags (via filter button).
    
*   Sorting of item list (by at least creation date).
    
*   Item cards displaying title, author, type, date of creation.
    
*   Item detail pages with the "Overview", "Tools", and "Integration" tabbed structure, populated with relevant structured metadata for each item type.
    
*   "Add to Workspace" functionality.
    
*   Responsive UI for desktop and mobile.
    
*   Basic accessibility features implemented.
    

**Future Enhancements**

*   Submission and management of ratings and reviews per item (display of average rating is MVP).
    
*   Author profiles and the ability to see the other tools and agents created by author.
    
*   Personalized item recommendations.
    
*   Dedicated category landing pages with featured items and more detailed category descriptions.
    
*   Advanced filtering options (e.g., by specific model for Agents, by version for Workflows/MCPs).
    
*   Installation instructions for any required SDKs (e.g., npm, pip commands), with copy-to-clipboard functionality.
    

**References:**

*   Developer Platform: [https://ruh-ai-developer-hub-16.lovable.app/](https://ruh-ai-developer-hub-16.lovable.app/)
    
*   Marketplace: [](https://agentic-nexus-flow.lovable.app/)https://agentic-nexus-flow-82.lovable.app/
    
*   Documentation: [https://ruh-ai-docs-design.lovable.app/](https://ruh-ai-docs-design.lovable.app/)