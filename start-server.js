#!/usr/bin/env node

/**
 * Server startup script for the Vite React application
 * This script handles starting the server in different environments
 * with appropriate configuration.
 */

import { spawn } from "child_process";
import { fileURLToPath } from "url";
import { dirname } from "path";

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Default configuration
const config = {
  host: process.env.HOST || "0.0.0.0",
  port: process.env.PORT || 3000,
  mode: process.env.NODE_ENV || "production",
};

// Log server startup information
console.log(`Starting server in ${config.mode} mode`);
console.log(
  `Server will be available at http://${
    config.host === "0.0.0.0" ? "localhost" : config.host
  }:${config.port}`
);

// Determine which command to run based on the environment
const command = config.mode === "production" ? "preview" : "dev";

// Path to local vite executable in node_modules
const viteExecutable = "./node_modules/.bin/vite";
const npxVite = "npx";

// Build the arguments array for vite directly
const args = [command, "--host", config.host, "--port", config.port];
const npxArgs = ["vite", ...args];

// Check if vite executable exists
import { existsSync } from "fs";
const useNpx = !existsSync(viteExecutable);

if (useNpx) {
  console.log(
    `Vite executable not found at ${viteExecutable}, using npx fallback`
  );
  console.log(`Executing: ${npxVite} ${npxArgs.join(" ")}`);

  // Use npx as a fallback
  const server = spawn(npxVite, npxArgs, {
    stdio: "inherit",
    cwd: __dirname,
  });

  // Handle process events for npx
  setupProcessHandlers(server);
} else {
  console.log(`Executing: ${viteExecutable} ${args.join(" ")}`);

  // Spawn the vite process directly
  const server = spawn(viteExecutable, args, {
    stdio: "inherit",
    cwd: __dirname,
  });

  // Handle process events for vite
  setupProcessHandlers(server);
}

// Function to set up process event handlers
function setupProcessHandlers(proc) {
  // Handle process events
  proc.on("error", (err) => {
    console.error("Failed to start server:", err);
    process.exit(1);
  });

  // Exit with the same code as the child process
  proc.on("close", (code) => {
    console.log(`Server process exited with code ${code}`);
    process.exit(code);
  });
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log("Gracefully shutting down server...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("Terminating server...");
  process.exit(0);
});
