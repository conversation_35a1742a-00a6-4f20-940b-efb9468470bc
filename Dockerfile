# Build stage
FROM node:20-alpine AS builder
WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./
COPY tsconfig*.json ./
RUN npm ci

# Copy source code and build
COPY . .
RUN npm run build

# Production stage
FROM node:20-alpine AS production
WORKDIR /app

# Set environment variables
ENV NODE_ENV=production
ENV VITE_USER_NODE_ENV=production

# Copy necessary files from builder
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/start-server.js ./start-server.js
COPY --from=builder /app/vite.config.ts ./vite.config.ts

# Make start script executable and ensure npx is available
RUN chmod +x start-server.js && npm install -g npm@latest

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application using our start script
CMD ["node", "start-server.js"]
